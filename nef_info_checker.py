#!/usr/bin/env python3
# NEF/JPG文件信息检查工具
#
# 功能说明：
# 1. 支持分析NEF(尼康RAW)和JPG格式文件
# 2. 提取完整的EXIF信息(使用exiftool)
# 3. 获取JPG文件尺寸
# 4. 提取关键对焦信息并计算对焦点位置
#
# 依赖项：
# - Python 3.x
# - Pillow库 (pip install pillow)
# - exiftool (需单独安装)
#
# 使用方法：
# python3 nef_info_checker.py [文件路径]
#
# 输出说明：
# - 文件基本信息(类型、路径)
# - 完整的EXIF信息
# - JPG文件尺寸(如存在)
# - 关键对焦信息(AF区域位置、尺寸)
# - 计算后的对焦点位置(相对于8256x5504)
#
# 注意事项：
# 1. 对于NEF文件会自动查找同名JPG文件
# 2. 可使用grep过滤特定信息
# 3. 需要exiftool在系统PATH中
# 输出模式控制
VERBOSE = False

import subprocess
import sys
import os
from PIL import Image

def get_all_exif(file_path):
    """获取文件的全部EXIF信息"""
    try:
        result = subprocess.run(
            ['exiftool', '-G1', '-a', '-u', file_path],
            capture_output=True, check=True
        )
        return result.stdout.decode('utf-8', errors='ignore')
    except Exception as e:
        print(f"获取EXIF信息失败: {e}")
        return None

def get_jpg_size(jpg_path):
    """获取JPG文件尺寸"""
    try:
        with Image.open(jpg_path) as img:
            return img.size
    except Exception as e:
        print(f"读取JPG尺寸失败: {e}")
        return None

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("用法: python3 nef_info_checker.py [文件路径] [-v]")
        print("示例: python3 nef_info_checker.py DSC_1234.NEF")
        print("示例: python3 nef_info_checker.py DSC_1234.JPG")
        print("添加 -v 参数显示详细输出")
        sys.exit(1)
    
    VERBOSE = '-v' in sys.argv
    file_path = sys.argv[1]
    file_path = sys.argv[1]
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在 {file_path}")
        sys.exit(1)

    file_ext = os.path.splitext(file_path)[1].lower()
    if file_ext == '.nef':
        # 查找同名JPG文件
        base_path = os.path.splitext(file_path)[0]
        jpg_path = base_path + '.JPG'
        if not os.path.exists(jpg_path):
            jpg_path = base_path + '.jpg'
    if VERBOSE:
        print(f"\n正在分析文件信息...")
    
    if file_ext == '.nef':
        if VERBOSE:
            print(f"NEF文件: {file_path}")
        # 获取NEF信息
        nef_exif = get_all_exif(file_path)
        if nef_exif:
            if VERBOSE:
                print("\nNEF文件EXIF信息:")
                print(nef_exif)
        
        # 获取JPG信息
        if os.path.exists(jpg_path):
            if VERBOSE:
                print(f"\n找到同名JPG文件: {jpg_path}")
            jpg_size = get_jpg_size(jpg_path)
            if jpg_size and VERBOSE:
                print(f"JPG文件尺寸: {jpg_size[0]}x{jpg_size[1]}")
            
            jpg_exif = get_all_exif(jpg_path)
            if jpg_exif:
                if VERBOSE:
                    if VERBOSE:
                        print("\nJPG文件EXIF信息:")
                        print(jpg_exif)
        elif VERBOSE:
            print("\n未找到同名JPG文件")
    elif file_ext in ('.jpg', '.jpeg'):
        if VERBOSE:
            print(f"JPG文件: {file_path}")
        jpg_size = get_jpg_size(file_path)
        if jpg_size and VERBOSE:
            print(f"JPG文件尺寸: {jpg_size[0]}x{jpg_size[1]}")
        
        jpg_exif = get_all_exif(file_path)
        if jpg_exif:
            print("\nJPG文件EXIF信息:")
            print(jpg_exif)
    else:
        print(f"错误: 不支持的文件类型 {file_ext}")
        sys.exit(1)

    # 提取关键对焦信息
    exif_data = nef_exif if file_ext == '.nef' else jpg_exif
    if exif_data:
        af_info = {
            'AF Area X Position': None,
            'AF Area Y Position': None,
            'AF Area Width': None,
            'AF Area Height': None,
            'AF Image Width': None,
            'AF Image Height': None
        }
        
        for line in exif_data.split('\n'):
            for key in af_info:
                if key in line:
                    value = line.split(':')[-1].strip()
                    af_info[key] = value
        
        if all(af_info.values()):
            print("计算后的对焦点位置(相对于8256x5504):")
            print(f"中心X: {int(af_info['AF Area X Position']) - int(af_info['AF Area Width'])//2}")
            print(f"中心Y: {int(af_info['AF Area Y Position']) - int(af_info['AF Area Height'])//2}")
            print(f"宽度: {af_info['AF Area Width']}")
            print(f"高度: {af_info['AF Area Height']}")