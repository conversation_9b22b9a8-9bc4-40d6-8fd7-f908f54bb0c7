# NCZ Photo Viewer GUI

基于YOLOv8的鸟类图片浏览与标记工具

## 功能特性
- 图片浏览与自动播放
- YOLOv8鸟类识别(支持6个类别: bird_eye, bird_clear, bird_flying, bird_back, bird_blurry, no_bird)
- 图片分类处理:
  - bird_eye/bird_clear/bird_flying: 保留在原目录
  - bird_back/bird_blurry/no_bird: 移动到no_bird目录
- 图片标记功能
- 断点续播功能
- 标记图片保存功能
- 键盘快捷键操作

## 快捷键说明
- 空格键: 播放/暂停
- 左箭头: 上一张图片
- 右箭头: 下一张图片
- Y键: 标记当前图片
- N键: 取消标记当前图片

## 使用说明
1. 安装依赖: `pip install -r requirements.txt`
2. 运行程序: `python NCZ_Photo_Viewer_GUI.py`
3. 选择图片目录和YOLO模型文件
4. 使用按钮或快捷键操作

## 打包为可执行文件
```bash
pyinstaller --onefile --windowed NCZ_Photo_Viewer_GUI.py