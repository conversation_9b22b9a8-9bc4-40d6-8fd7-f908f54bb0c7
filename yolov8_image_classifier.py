#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv8 鸟类图片分类器

[功能概述]
基于YOLOv8模型对鸟类图片进行分类，自动将图片分为两类：
1. 低质量/非目标图片(bird_blurry/bird_back/no_bird)
2. 高质量目标图片(bird_eye/bird_clear/bird_flying)

[分类标准]
1. 第一类(移动到no_bird子目录):
   - bird_blurry(2): 模糊的鸟类图片
   - bird_back(3): 只拍到鸟类背面的图片
   - no_bird(5): 不含鸟类的图片

2. 第二类(保留在源目录):
   - bird_eye(0): 清晰的鸟类眼部特写
   - bird_clear(1): 清晰的鸟类全身/半身照
   - bird_flying(4): 飞行中的鸟类

[技术实现]
1. 模型加载:
   - 使用Ultralytics YOLOv8模型
   - 默认模型路径: /Users/<USER>/Desktop/YOLOv8-11/Model_train/250701_v1_v11m/best.pt

2. 预测流程:
   - 对每张图片运行模型预测
   - 分析检测框的类别ID
   - 根据类别ID确定分组

3. 文件处理:
   - 自动创建no_bird子目录
   - 移动主图片文件(JPG/PNG)
   - 自动匹配并移动同名NEF文件(如果存在)

[使用方式]
1. 命令行模式:
   python3 yolov8_image_classifier.py --input <图片目录> [--model <模型路径>]

2. 交互模式(无参数):
   - 自动扫描桌面NCZ_9目录
   - 支持编号选择或前缀匹配

[日志系统]
- 文件: yolov8_classifier.log
- 格式: 时间 - 级别 - 消息
- 记录: 处理状态、移动操作、错误信息

[注意事项]
1. 需要预先安装Ultralytics YOLOv8
2. 模型文件需放在指定路径
3. 仅处理JPG/PNG图片
4. 会自动处理同名NEF文件
"""

import os
import shutil
import argparse
import time
from ultralytics import YOLO
from datetime import datetime

# 配置日志
def format_hh_mm(seconds):
    """将秒转换为小时:分钟格式"""
    m, _ = divmod(seconds, 60)
    h, m = divmod(m, 60)
    return f"{int(h):02d}:{int(m):02d}"

script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, "yolov8_classifier.log")

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        filename=log_file,
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console = logging.StreamHandler()
    console.setLevel(logging.INFO)
    logging.getLogger('').addHandler(console)

def classify_images(model_path, input_dir, output_dir):
    """
    分类图片到不同组别
    返回: 总处理图片数, 总耗时(秒)
    :param model_path: YOLOv8模型路径
    :param input_dir: 输入图片目录
    :param output_dir: 输出根目录
    """
    # 直接在输入目录下创建分类目录
    no_bird_dir = os.path.join(input_dir, "no_bird")  # 存放no_bird/bird_back/bird_blurry
    os.makedirs(no_bird_dir, exist_ok=True)
    
    total_images = 0
    start_time = time.time()
    
    # 加载模型
    model = YOLO(model_path)
    logging.info(f"Loaded model from {model_path}")
    
    # 处理图片
    # 获取所有图片文件(JPG/PNG)和对应的NEF文件
    image_files = [f for f in os.listdir(input_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    for filename in image_files:
        img_start_time = time.time()
        input_path = os.path.join(input_dir, filename)
        
        try:
            # 预测
            results = model.predict(input_path)
            
            # 分析结果
            group = None
            if len(results) > 0 and hasattr(results[0], 'boxes'):
                for box in results[0].boxes:
                    cls_id = int(box.cls)
                    # 第一类: 2=bird_blurry, 3=bird_back, 5=no_bird
                    if cls_id in [2, 3, 5]:
                        group = "group1"
                        break
                    # 第二类: 0=bird_eye, 1=bird_clear, 4=bird_flying
                    elif cls_id in [0, 1, 4]:
                        group = "group2"
                        break
            
            # 默认放入group1
            if group is None:
                group = "group1"
                logging.info(f"No bird detected in {filename}, moved to no_bird directory")
            
            # 移动文件
            if group == "group1":
                output_path = os.path.join(no_bird_dir, filename)
            else:
                # group2直接放在输入目录下
                output_path = os.path.join(input_dir, filename)
                
            try:
                # 移动主图片文件
                shutil.move(input_path, output_path)
                logging.info(f"Moved {filename} to {'no_bird' if group == 'group1' else 'input directory'}")
                
                # 查找并移动同名的NEF文件
                if group == "group1":
                    base_name = os.path.splitext(filename)[0]
                    nef_file = f"{base_name}.NEF"
                    nef_path = os.path.join(input_dir, nef_file)
                    if os.path.exists(nef_path):
                        shutil.move(nef_path, os.path.join(no_bird_dir, nef_file))
                        logging.info(f"Also moved {nef_file} to no_bird")
            
                img_time = time.time() - img_start_time
                logging.info(f"Processed {filename} in {int(img_time*1000)}ms")
            
            except Exception as e:
                logging.error(f"Failed to move {filename}: {str(e)}")
            
        except Exception as e:
            logging.error(f"Error processing {filename}: {str(e)}")
    
    total_time = time.time() - start_time
    return total_images, total_time

if __name__ == "__main__":
    import logging
    setup_logging()
    
    # 参数解析器
    parser = argparse.ArgumentParser(
        description='YOLOv8鸟类图片分类器\n'
        '将图片分为两类:\n'
        '1. bird_blurry/bird_back/no_bird -> 移动到no_bird子目录\n'
        '2. bird_eye/bird_clear/bird_flying -> 保留在源目录',
        formatter_class=argparse.RawTextHelpFormatter)
    
    parser.add_argument('--model', default='/Users/<USER>/Desktop/YOLOv8-11/Model_train/250701_v1_v11m/best.pt',
                      help='YOLOv8模型路径 (默认: %(default)s)')
    parser.add_argument('--input', help='输入图片目录路径')
    parser.add_argument('--output', help='输出目录路径')
    
    try:
        args = parser.parse_args()
        
        # 如果没有提供输入输出参数，使用交互式选择
        if not args.input or not args.output:
            try:
                from ncz_directory_selector import select_directory, find_ncz_directories
                
                base_path = "/Users/<USER>/Desktop"
                ncz_dirs = find_ncz_directories(base_path)
                if not ncz_dirs:
                    logging.error("未找到NCZ_9格式的目录")
                    exit(1)
                    
                selected_dir = select_directory(ncz_dirs)
                if not selected_dir:
                    exit(0)
                    
                # 设置输入目录
                args.input = os.path.join(base_path, selected_dir)
                
                # 检查输入目录是否存在
                if not os.path.exists(args.input):
                    logging.error(f"输入目录不存在: {args.input}")
                    exit(1)
            except ImportError:
                logging.error("缺少ncz_directory_selector模块")
                parser.print_help()
                exit(1)
    except SystemExit:
        # 显示帮助信息后退出
        exit(1)
        
    logging.info("="*50)
    logging.info(f"Starting classification at {datetime.now()}")
    logging.info(f"Input directory: {args.input}")
    logging.info(f"Output directory: {args.output}")
    
    processed_count, total_time = classify_images(args.model, args.input, args.output)
    
    logging.info("Classification completed")
    logging.info(f"Total images processed: {processed_count}")
    logging.info(f"Total time: {format_hh_mm(total_time)}")
    logging.info(f"Average time per image: {format_hh_mm(total_time/processed_count)}" if processed_count > 0 else "")
    logging.info("="*50)
