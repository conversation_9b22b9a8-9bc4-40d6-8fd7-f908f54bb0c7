#!/usr/bin/env python3
"""
NEF对焦点标记工具

[功能概述]
自动读取尼康NEF(RAW)文件中的对焦点位置信息，并在同名JPG文件上标记对焦点区域。
支持批量处理目录下所有NEF文件，自动跳过无效或超大对焦点区域。

[主要功能]
1. 对焦点信息提取
  - 使用exiftool读取AFAreaX/YPosition和AFAreaWidth/Height
  - 自动验证对焦点尺寸(默认≤600x600)

2. 图像标记处理
  - 基于NEF全尺寸(8256x5504)到实际JPG尺寸的坐标转换
  - 动态调整角标长度(小区域20px，大区域30px)
  - 洋红色(255,0,255)5px宽矩形框标记

3. 文件处理模式
  - 交互模式：自动查找桌面NCZ_9目录，支持编号/前缀选择
  - 命令行模式：直接指定NEF文件或目录路径

[处理流程]
1. 读取NEF文件EXIF信息 → 2. 验证对焦点尺寸 → 3. 计算JPG坐标 →
4. 绘制标记 → 5. 保存输出 → 6. 记录日志

[使用方式]
1. 交互模式(无参数):
  python3 mark_focus_point.py
  → 自动列出桌面NCZ_9目录供选择

2. 命令行模式:
  python3 mark_focus_point.py <NEF文件或目录路径>

[输出说明]
- 直接修改原始JPG文件(非创建副本)
- 日志文件: mark_focus_point.log
 格式: 时间 [脚本名] 处理状态 JPG路径 NEF路径

[注意事项]
1. 需预先安装exiftool并添加到PATH
2. JPG文件需与NEF同名且同目录
3. 仅处理对焦点尺寸≤600x600的情况
"""

import os
import subprocess
import time
import logging
import shutil
from PIL import Image, ImageDraw

# 设置日志
script_name = os.path.splitext(os.path.basename(__file__))[0]
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), f"{script_name}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(script_name)

start_time = time.time()

def get_focus_info(nef_path):
    """使用exiftool获取对焦点信息"""
    cmd = f'exiftool -AFAreaXPosition -AFAreaYPosition -AFAreaWidth -AFAreaHeight {nef_path}'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    # 解析输出
    values = []
    for line in result.stdout.splitlines():
        if ":" in line:
            value = line.split(":")[1].strip()
            try:
                values.append(int(value))
            except ValueError:
                continue
    
    if len(values) != 4:
        raise ValueError(f"无法解析对焦点信息，获取到{len(values)}个值，需要4个")
    
    return tuple(values)

def mark_focus_point(jpg_path, x, y, w, h, color=(255, 0, 255), width=5):
    """在JPG图像上标记对焦点区域"""
    img = Image.open(jpg_path)
    draw = ImageDraw.Draw(img)
    
    # 计算缩放比例(假设JPG是从NEF全尺寸导出)
    scale_x = img.width / 8256
    scale_y = img.height / 5504
    
    # 计算JPG上的对焦点坐标和尺寸
    jpg_x = int(x * scale_x)
    jpg_y = int(y * scale_y)
    jpg_w = int(w * scale_x)
    jpg_h = int(h * scale_y)
    
    # 计算对焦点区域边界
    left = jpg_x - jpg_w//2
    right = jpg_x + jpg_w//2
    top = jpg_y - jpg_h//2
    bottom = jpg_y + jpg_h//2
    
    # 根据AF区域大小动态调整角标长度
    corner_length = 20 if w < 160 and h < 160 else 30
    
    # 绘制完整矩形框
    draw.rectangle([left, top, right, bottom], outline=color, width=width)
    
    # 保存文件
    img.save(jpg_path)
    return jpg_path

def process_directory(directory):
    """处理目录下所有NEF文件"""
    processed_count = 0
    skipped_count = 0
    error_count = 0
    
    # 不再创建focus_area目录
    
    for filename in os.listdir(directory):
        if filename.upper().endswith('.NEF'):
            nef_path = os.path.join(directory, filename)
            jpg_path = os.path.splitext(nef_path)[0] + '.JPG'
            
            if not os.path.exists(jpg_path):
                print(f"警告: 跳过 {filename}, 找不到对应的JPG文件")
                skipped_count += 1
                continue
                
            try:
                x, y, w, h = get_focus_info(nef_path)
                if w <= 600 and h <= 600:
                    output_path = mark_focus_point(jpg_path, x, y, w, h)
                    # 使用绝对路径记录日志
                    abs_jpg = os.path.abspath(jpg_path)
                    abs_nef = os.path.abspath(nef_path)
                    logger.info(f"PROCESSED {abs_jpg} {abs_nef}")
                    print(f"记录处理日志: {abs_jpg} {abs_nef}")
                    print(f"成功处理: {filename} -> {os.path.basename(output_path)}")
                    processed_count += 1
                else:
                    print(f"跳过: {filename} 对焦点尺寸{w}x{h}不符合要求(需<=600)")
                    skipped_count += 1
            except Exception as e:
                logger.error(f"处理 {filename} 时出错: {e}")
                print(f"处理 {filename} 时出错: {e}")
                error_count += 1
    
    elapsed_time = time.time() - start_time
    hours = int(elapsed_time // 3600)
    minutes = int((elapsed_time % 3600) // 60)
    seconds = int(elapsed_time % 60)
    
    print(f"\n处理完成 - 总耗时: {hours:02d}:{minutes:02d}:{seconds:02d}")
    print(f"成功处理: {processed_count} 个文件")
    print(f"跳过处理: {skipped_count} 个文件")
    print(f"处理出错: {error_count} 个文件")

def find_ncz_dirs():
    """查找桌面下的NCZ_9格式目录"""
    desktop = os.path.expanduser("~/Desktop")
    dirs = []
    for name in os.listdir(desktop):
        if name.endswith("NCZ_9") and os.path.isdir(os.path.join(desktop, name)):
            dirs.append(name)
    return sorted(dirs)


if __name__ == '__main__':
    import sys
    import shutil
    
    # 交互模式
    if len(sys.argv) == 1:
        dirs = find_ncz_dirs()
        if not dirs:
            print("在桌面未找到NCZ_9格式目录")
            sys.exit(1)
            
        print("在 /Users/<USER>/Desktop 下找到以下NCZ_9格式目录:")
        for i, dirname in enumerate(dirs, 1):
            print(f"{i}. {dirname}")
            
        while True:
            choice = input("输入目录名前3位数字或编号(输入q退出): ").strip()
            if choice.lower() == 'q':
                sys.exit(0)
                
            # 尝试匹配编号
            if choice.isdigit() and 1 <= int(choice) <= len(dirs):
                path = os.path.join(os.path.expanduser("~/Desktop"), dirs[int(choice)-1])
                break
            # 尝试匹配前缀
            elif len(choice) == 3 and choice.isdigit():
                matched = [d for d in dirs if d.startswith(choice)]
                if len(matched) == 1:
                    path = os.path.join(os.path.expanduser("~/Desktop"), matched[0])
                    break
                elif len(matched) > 1:
                    print(f"找到多个匹配目录: {', '.join(matched)}")
                else:
                    print("未找到匹配目录")
            else:
                print("无效输入，请重试")
    
    # 命令行模式
    elif len(sys.argv) == 2:
        path = sys.argv[1]
    else:
        print("用法: python mark_focus_point.py [目录路径]")
        sys.exit(1)
    
    # 处理选定的路径
    if os.path.isdir(path):
        print(f"\n正在处理目录: {path}")
        process_directory(path)
    else:
        # 处理单个文件
        jpg_path = os.path.splitext(path)[0] + '.JPG'
        if not os.path.exists(jpg_path):
            print(f"错误: 找不到对应的JPG文件 {jpg_path}")
            sys.exit(1)
            
        try:
            x, y, w, h = get_focus_info(path)
            if w <= 600 and h <= 600:
                output_path = mark_focus_point(jpg_path, x, y, w, h)
                print(f"成功: 已标记对焦点 {output_path}")
            else:
                print(f"跳过: 对焦点尺寸{w}x{h}不符合要求(需<=600)")
        except Exception as e:
            print(f"错误: {e}")
            logger.error(f"处理单个文件时出错: {e}")

