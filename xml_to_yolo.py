import os
import xml.etree.ElementTree as ET

def convert_xml_to_yolo(xml_dir, output_dir, class_mapping):
    """
    将LabelImg生成的XML标注文件转换为YOLO格式的TXT文件
    
    参数:
        xml_dir: 包含XML文件的目录路径
        output_dir: 输出TXT文件的目录路径
        class_mapping: 类别名称到ID的映射字典
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for xml_file in os.listdir(xml_dir):
        if not xml_file.endswith('.xml'):
            continue

        xml_path = os.path.join(xml_dir, xml_file)
        tree = ET.parse(xml_path)
        root = tree.getroot()

        # 获取图片尺寸用于坐标归一化
        size = root.find('size')
        img_width = int(size.find('width').text)
        img_height = int(size.find('height').text)

        # 准备YOLO格式内容
        yolo_lines = []
        for obj in root.iter('object'):
            cls_name = obj.find('name').text
            if cls_name not in class_mapping:
                continue

            cls_id = class_mapping[cls_name]
            bbox = obj.find('bndbox')
            xmin = float(bbox.find('xmin').text)
            ymin = float(bbox.find('ymin').text)
            xmax = float(bbox.find('xmax').text)
            ymax = float(bbox.find('ymax').text)

            # 转换为YOLO格式(中心点坐标和宽高，归一化)
            x_center = ((xmin + xmax) / 2) / img_width
            y_center = ((ymin + ymax) / 2) / img_height
            width = (xmax - xmin) / img_width
            height = (ymax - ymin) / img_height

            yolo_lines.append(f"{cls_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")

        # 写入TXT文件
        txt_filename = os.path.splitext(xml_file)[0] + '.txt'
        txt_path = os.path.join(output_dir, txt_filename)
        with open(txt_path, 'w') as f:
            f.write('\n'.join(yolo_lines))

if __name__ == "__main__":
    # 按照用户指定的ID分配方案
    CLASS_MAPPING = {
        'bird_eye': 0,
        'bird_clear': 1,
        'bird_flying': 2,
        'bird_blurry': 3,
        'flying_blurry': 4
    }

    XML_DIR = '/Users/<USER>/Desktop/bird_eye_dataset'
    OUTPUT_DIR = '/Users/<USER>/Desktop/bird_eye_dataset/labels'

    # 执行转换
    convert_xml_to_yolo(XML_DIR, OUTPUT_DIR, CLASS_MAPPING)
    print(f"转换完成，结果保存在: {OUTPUT_DIR}")