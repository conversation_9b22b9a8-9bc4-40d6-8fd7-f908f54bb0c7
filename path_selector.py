#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件路径选择模块

功能：
1. 提供交互式目录选择界面
2. 支持输入前3位数字快速定位目录
3. 返回绝对路径供主程序使用
"""

import os
import sys
import logging

def setup_logging(directory=None):
    """配置模块日志"""
    script_name = os.path.splitext(os.path.basename(__file__))[0]
    if directory:
        log_file = os.path.join(directory, f"{script_name}.log")
    else:
        log_file = os.path.abspath(f"{script_name}.log")
    logging.basicConfig(
        filename=log_file,
        level=logging.INFO,
        format='%(asctime)s [%(name)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(script_name)

global logger
logger = setup_logging()  # 初始化默认logger

def select_directory():
    """
    交互式目录选择
    仅在/Users/<USER>/Desktop下查找数字+NCZ_9格式的目录
    
    :return: 选择的目录绝对路径
    """
    base_path = "/Users/<USER>/Desktop"
    """
    交互式目录选择
    
    :param base_path: 基础搜索路径
    :return: 选择的目录绝对路径
    """
    try:
        print(f"\n在 {base_path} 下找到以下NCZ_9格式目录:")
        dirs = [d for d in os.listdir(base_path)
               if os.path.isdir(os.path.join(base_path, d))
               and d[:3].isdigit()
               and (d.upper().endswith("NCZ_9") or d.upper().endswith("NCZ9"))]
        
        for i, dir_name in enumerate(sorted(dirs), 1):
            print(f"{i}. {dir_name}")
            
        while True:
            while True:
                choice = input("\n输入目录名前3位数字或编号(输入q退出): ").strip()
                if choice.lower() == 'q':
                    return None
                
                # 处理列表编号选择
                if choice.isdigit() and len(choice) <= 2:
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(dirs):
                        selected = dirs[choice_num-1]
                        full_path = os.path.join(base_path, selected)
                        logger = setup_logging(full_path)
                        logger.info(f"选择目录: {full_path}")
                        return full_path
                    print("编号无效，请重新输入")
                    continue
                
                # 处理3位数字前缀输入
                if len(choice) == 3 and choice.isdigit():
                    matches = []
                    for d in dirs:
                        if d.startswith(choice):
                            matches.append(d)
                    
                    if len(matches) == 1:
                        full_path = os.path.join(base_path, matches[0])
                        logger = setup_logging(full_path)
                        logger.info(f"快速选择目录: {full_path}")
                        return full_path
                    elif matches:
                        print(f"找到{len(matches)}个匹配目录:")
                        for i, match in enumerate(matches, 1):
                            print(f"{i}. {match}")
                        continue
                    
                    print(f"没有找到以 {choice} 开头的NCZ_9目录")
                    continue
                
                print("请输入3位数字前缀或有效编号")
                print(f"没有找到以 {choice} 开头的NCZ_9目录")
            else:
                print("请输入至少3位数字或有效编号")
                
    except Exception as e:
        logger.error(f"目录选择错误: {str(e)}")
        print(f"发生错误: {str(e)}")
        return None

if __name__ == '__main__':
    selected = select_directory()
    print(f"\n选择的目录: {selected}" if selected else "已取消选择")