#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv8图片查看标记器
- 版本: 1.0
- 主要功能: 使用OpenCV播放指定目录下的JPG文件，并支持YOLOv8检测和标记
- 支持大小写JPG扩展名
- 自动播放功能，可暂停/继续
- YOLOv8目标检测和可视化
- 图片标记功能
- 播放进度显示
"""

import os
import cv2
import time
import json
import datetime
import argparse
from ultralytics import YOLO

def get_playback_log(directory):
    """获取播放日志文件路径"""
    return os.path.join(directory, "playback_log.json")

def get_last_position(directory):
    """获取上次退出位置"""
    log_file = get_playback_log(directory)
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r') as f:
                log = json.load(f)
                if 'last_position' in log:
                    print(f"从播放日志恢复位置: 第{log['last_position']+1}张图片 (时间: {log.get('timestamp', '未知')})")
                    return log['last_position']
                else:
                    print("播放日志格式错误，将从头开始播放")
        except Exception as e:
            print(f"读取播放日志时出错: {e}")
    
    # 没有找到任何位置记录
    print("未找到播放记录，将从头开始播放")
    return 0

def update_playback_log(directory, position):
    """更新播放日志"""
    log_file = get_playback_log(directory)
    log_data = {
        'last_position': position,
        'timestamp': datetime.datetime.now().isoformat(),
        'directory': directory
    }
    
    try:
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2)
        print(f"已更新播放日志: 第{position+1}张图片")
    except Exception as e:
        print(f"保存播放日志时出错: {e}")

def load_marked_images(directory):
    """加载已标记的图片列表"""
    marked_images = set()
    tags_file = os.path.join(directory, "photo_tags.txt")
    if os.path.exists(tags_file):
        try:
            with open(tags_file, 'r') as f:
                for line in f:
                    marked_images.add(line.strip())
            print(f"已加载 {len(marked_images)} 个标记")
        except Exception as e:
            print(f"读取标记文件时出错: {e}")
    return marked_images

def mark_image(directory, filename, marked_images):
    """标记图片"""
    marked_images.add(filename)
    
    tags_file = os.path.join(directory, "photo_tags.txt")
    
    try:
        with open(tags_file, 'w') as f:
            for img in sorted(marked_images):
                f.write(f"{img}\n")
        print(f"已标记图片: {filename}")
        return True
    except Exception as e:
        print(f"保存标记时出错: {e}")
        return False

def unmark_image(directory, filename, marked_images):
    """取消标记图片"""
    if filename in marked_images:
        marked_images.remove(filename)
        
        tags_file = os.path.join(directory, "photo_tags.txt")
        try:
            with open(tags_file, 'w') as f:
                for img in sorted(marked_images):
                    f.write(f"{img}\n")
            print(f"已取消标记图片: {filename}")
            return True
        except Exception as e:
            print(f"保存标记时出错: {e}")
    return False

def view_images(directory, model_path=None):
    """播放目录中的JPG图片，支持YOLOv8检测和标记"""
    
    # 加载YOLOv8模型（如果提供）
    model = None
    if model_path and os.path.exists(model_path):
        try:
            model = YOLO(model_path)
            print(f"已加载YOLOv8模型: {model_path}")
            
            # 打印原始模型类别
            print(f"模型原始类别: {model.names}")
            
            # 不修改模型类别，使用模型自带的类别
            print(f"使用模型原始类别: {model.names}")
            
        except Exception as e:
            print(f"加载YOLOv8模型失败: {e}")
            model = None
    else:
        print("未提供模型路径或模型文件不存在，将不进行目标检测")
    
    # 获取目录中所有JPG文件
    image_files = []
    for filename in os.listdir(directory):
        ext = os.path.splitext(filename)[1].lower()
        if ext in ['.jpg', '.jpeg']:
            image_files.append(os.path.join(directory, filename))
    
    if not image_files:
        print(f"错误: 目录 '{directory}' 中没有JPG图片!")
        return
    
    # 排序文件名
    image_files.sort()
    
    # 加载已标记的图片
    marked_images = load_marked_images(directory)
    
    # 获取上次退出位置
    current_index = get_last_position(directory)
    if current_index >= len(image_files):
        print("上次位置超出当前图片数量，从头开始播放")
        current_index = 0
    
    # 创建窗口
    window_name = "YOLOv8 Image Viewer (Space:Pause/Play  y/n:Mark/Unmark  Left/Right:Navigate  q:Quit)"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    
    print(f"开始播放 {len(image_files)} 张图片")
    print("控制说明:")
    print("  空格键: 暂停/继续播放")
    print("  y键: 标记当前图片")
    print("  n键: 取消标记当前图片")
    print("  左右方向键: 浏览前后图片")
    print("  q键: 退出")
    
    # 播放参数
    auto_play = True
    last_change_time = time.time()
    display_time = 500  # 默认500毫秒
    
    # 字体参数 - 稍微缩小字体
    font_face = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 4.5  # 从5.0稍微缩小到4.5
    font_thickness = 6  # 保持粗细不变
    text_color_info = (0, 255, 0)  # 绿色
    text_color_mark = (0, 0, 255)  # 红色
    
    # 检测框参数 - 增加线宽以适应更大窗口
    detection_line_width = 5
    
    try:
        while current_index < len(image_files):
            # 读取当前图片
            img_path = image_files[current_index]
            filename = os.path.basename(img_path)
            
            print(f"正在显示: [{current_index+1}/{len(image_files)}] {filename}")
            
            # 读取图片
            img = cv2.imread(img_path)
            if img is None:
                print(f"无法读取图片: {img_path}")
                current_index += 1
                continue
            
            # 调整窗口大小 - 使用更大的显示尺寸
            h, w = img.shape[:2]
            screen_w, screen_h = 1920, 1080  # 增加屏幕尺寸
            scale = min(screen_w / w, screen_h / h) * 0.9  # 增加缩放比例到90%
            new_w, new_h = int(w * scale), int(h * scale)
            cv2.resizeWindow(window_name, new_w, new_h)
            
            # 复制图片用于绘制
            display_img = img.copy()
            
            # 使用YOLOv8进行检测（如果模型可用）
            if model is not None:
                try:
                    # 添加调试信息
                    print(f"正在对图片进行YOLOv8检测: {filename}")
                    
                    # 使用更高的置信度阈值进行检测
                    results = model.predict(img, conf=0.25, verbose=False)
                    
                    # 检查结果
                    if len(results) > 0:
                        print(f"检测到 {len(results)} 个结果")
                        
                        if hasattr(results[0], 'boxes') and results[0].boxes is not None:
                            boxes = results[0].boxes
                            print(f"检测到 {len(boxes)} 个边界框")
                            
                            # 绘制检测结果
                            for i, box in enumerate(boxes):
                                # 获取边界框坐标
                                xyxy = box.xyxy[0].cpu().numpy()
                                cls_id = int(box.cls)
                                conf = float(box.conf)
                                
                                # 获取类别名称
                                cls_name = model.names[cls_id] if cls_id < len(model.names) else f"class_{cls_id}"
                                
                                print(f"  框 {i+1}: 类别={cls_name} ({cls_id}), 置信度={conf:.2f}, 坐标={xyxy}")
                                
                                # 设置颜色（根据类别或置信度）
                                if "bird_eye" in cls_name:
                                    color = (0, 0, 255)  # 红色
                                elif "focus_area" in cls_name:
                                    color = (255, 0, 255)  # 洋红色
                                else:
                                    color = (0, 255, 0)  # 绿色
                                
                                # 确保坐标是整数
                                x1, y1, x2, y2 = map(int, xyxy)
                                
                                # 绘制检测框 - 使用更粗的线条
                                cv2.rectangle(display_img,
                                            (x1, y1),
                                            (x2, y2),
                                            color, detection_line_width)
                                
                                # 绘制标签 - 增大检测框标签字体
                                label = f"{cls_name} {conf:.2f}"
                                text_pos = (x1, y1 - 55)  # 从y1-35改为y1-55，再向上移动20像素

                                # 添加文字背景 - 使用更大的字体
                                label_font_scale = 3.0  # 从1.5增大到3.0
                                label_thickness = 3
                                (text_width, text_height), _ = cv2.getTextSize(label, font_face, label_font_scale, label_thickness)
                                cv2.rectangle(display_img,
                                            (text_pos[0], text_pos[1] - text_height - 8),
                                            (text_pos[0] + text_width, text_pos[1] + 8),
                                            (0, 0, 0), -1)

                                # 绘制文字
                                cv2.putText(display_img, label, text_pos, font_face, label_font_scale, (255, 255, 255), label_thickness)
                            
                except Exception as e:
                    print(f"YOLOv8检测失败: {e}")
            
            # 添加播放信息文字 - 使用更大字体
            info_text = f"[{current_index+1}/{len(image_files)}] {filename}"
            if filename in marked_images:
                info_text += " [MARKED]"
                text_color = text_color_mark
            else:
                text_color = text_color_info
            
            # 添加播放时长信息
            info_text += f" [{display_time}ms]"
            
            # 在图片上显示信息 - 调整位置以适应更大字体
            # 向下移100px，向右移100px (从50,120变为150,220)
            
            # 添加黑色背景 - 固定高度为180px
            (text_width, text_height), _ = cv2.getTextSize(info_text, font_face, font_scale, font_thickness)
            bg_top = 220 - text_height - (180 - text_height) // 2  # 计算背景顶部位置
            bg_bottom = bg_top + 180  # 底部就是顶部+180px
            cv2.rectangle(display_img,
                        (150, bg_top),
                        (150 + text_width + 20, bg_bottom),
                        (0, 0, 0), -1)  # 黑色填充背景
                        
            cv2.putText(display_img, info_text, (150, 220), font_face, font_scale, text_color, font_thickness)

            # 显示暂停状态 - 同样使用更大字体
            # 向下移200px (从50,250变为150,450)
            if not auto_play:
                # 添加黑色背景 - 固定高度为180px
                pause_text = "PAUSED"
                (pause_width, pause_height), _ = cv2.getTextSize(pause_text, font_face, font_scale, font_thickness)
                bg_top = 450 - pause_height - (180 - pause_height) // 2  # 计算背景顶部位置
                bg_bottom = bg_top + 180  # 底部就是顶部+180px
                cv2.rectangle(display_img,
                            (150, bg_top),
                            (150 + pause_width + 20, bg_bottom),
                            (0, 0, 0), -1)  # 黑色填充背景
                            
                cv2.putText(display_img, pause_text, (150, 450), font_face, font_scale, text_color_mark, font_thickness)
            
            # 显示图片
            cv2.imshow(window_name, display_img)
            
            # 处理键盘输入
            key = cv2.waitKey(50) & 0xFF
            
            if key == ord('q') or key == 27:  # q键或ESC键退出
                # 保存当前位置
                update_playback_log(directory, current_index)
                break
            elif key == ord(' '):  # 空格键暂停/继续
                auto_play = not auto_play
                if auto_play:
                    last_change_time = time.time()
                print(f"播放状态: {'继续' if auto_play else '暂停'}")
            elif key == ord('y'):  # y键标记
                mark_image(directory, filename, marked_images)
            elif key == ord('n'):  # n键取消标记
                unmark_image(directory, filename, marked_images)
            elif key == ord('+') or key == ord('='):  # +键增加播放时长
                display_time += 100
                print(f"播放时长增加到: {display_time}ms")
            elif key == ord('-') or key == ord('_'):  # -键减少播放时长
                display_time = max(100, display_time - 100)  # 最小100ms
                print(f"播放时长减少到: {display_time}ms")
            elif key == 81 or key == 2:  # 左方向键
                if current_index > 0:
                    current_index -= 1
                    auto_play = False
                    continue
            elif key == 83 or key == 3:  # 右方向键
                if current_index < len(image_files) - 1:
                    current_index += 1
                    auto_play = False
                    continue
            
            # 自动播放逻辑
            if auto_play and time.time() - last_change_time >= display_time/1000.0:  # 转换为秒
                current_index += 1
                last_change_time = time.time()
                # 更新播放日志
                update_playback_log(directory, current_index)
        
        print("播放完成!")
        # 播放完成后，重置位置为0
        update_playback_log(directory, 0)
        
    except KeyboardInterrupt:
        print("\n用户中断播放")
        # 保存当前位置
        update_playback_log(directory, current_index)
    finally:
        # 保存标记的文件
        if marked_images:
            save_marked_files(directory, marked_images)
        
        cv2.destroyAllWindows()

def save_marked_files(directory, marked_images):
    """保存标记的NEF+JPG对到桌面"""
    if not marked_images:
        print("没有标记的图片，跳过保存步骤")
        return None
    
    print("\n开始保存标记的NEF+JPG对...")
    
    # 获取源目录名称（不含路径）
    source_dir_name = os.path.basename(directory)
    
    # 创建桌面上的筛选结果目录
    results_dir = "/Users/<USER>/Desktop/筛选结果"
    os.makedirs(results_dir, exist_ok=True)
    
    # 创建保存目标目录（在筛选结果目录内）
    saved_target_dir = os.path.join(results_dir, f"{source_dir_name}_保存")
    os.makedirs(saved_target_dir, exist_ok=True)
    
    # 复制标记的文件
    copy_count = 0
    nef_count = 0
    
    # 复制标记的JPG文件及其对应的NEF文件
    for jpg_filename in marked_images:
        # 跳过非JPG文件
        if not jpg_filename.lower().endswith(('.jpg', '.jpeg')):
            continue
        
        # 获取基本文件名（不含扩展名）
        base = os.path.splitext(jpg_filename)[0]
        
        # 复制JPG文件
        src_jpg_path = os.path.join(directory, jpg_filename)
        dst_jpg_path = os.path.join(saved_target_dir, jpg_filename)
        
        if os.path.exists(src_jpg_path):
            import shutil
            shutil.copy2(src_jpg_path, dst_jpg_path)
            copy_count += 1
            print(f"已复制JPG: {jpg_filename}")
        
        # 检查并复制对应的NEF文件
        nef_filename = f"{base}.NEF"
        src_nef_path = os.path.join(directory, nef_filename)
        
        if os.path.exists(src_nef_path):
            dst_nef_path = os.path.join(saved_target_dir, nef_filename)
            shutil.copy2(src_nef_path, dst_nef_path)
            nef_count += 1
            print(f"已复制NEF: {nef_filename}")
    
    print(f"\n保存完成！")
    print(f"已复制 {copy_count} 个标记的JPG文件")
    print(f"已复制 {nef_count} 个对应的NEF文件")
    print(f"文件已保存到: {saved_target_dir}")
    
    return saved_target_dir

def select_directory():
    """交互式选择目录"""
    base_path = "/Users/<USER>/Desktop"
    
    # 查找符合条件的目录
    ncz_dirs = []
    for item in os.listdir(base_path):
        full_path = os.path.join(base_path, item)
        if os.path.isdir(full_path) and len(item) >= 7:
            # 检查前三位是否为数字，后缀是否为NCZ_9
            if item[:3].isdigit() and item.upper().endswith("NCZ_9"):
                ncz_dirs.append(item)
    
    if not ncz_dirs:
        print(f"在 {base_path} 下没有找到符合条件的目录 (前三位数字 + NCZ_9)")
        return None
    
    # 按名称排序
    ncz_dirs.sort()
    
    # 显示可选目录
    print(f"\n在 {base_path} 下找到以下NCZ_9目录:")
    for i, dir_name in enumerate(ncz_dirs, 1):
        print(f"{i}. {dir_name}")
    
    # 用户选择
    while True:
        choice = input("\n请输入目录前三位数字或编号 (q退出): ").strip()
        
        if choice.lower() == 'q':
            return None
        
        # 处理编号选择
        if choice.isdigit() and 1 <= int(choice) <= len(ncz_dirs):
            selected_dir = ncz_dirs[int(choice) - 1]
            return os.path.join(base_path, selected_dir)
        
        # 处理三位数字选择
        if len(choice) == 3 and choice.isdigit():
            # 查找匹配的目录
            matches = [d for d in ncz_dirs if d.startswith(choice)]
            if len(matches) == 1:
                return os.path.join(base_path, matches[0])
            elif matches:
                print(f"找到多个匹配的目录:")
                for i, match in enumerate(matches, 1):
                    print(f"{i}. {match}")
                continue
            else:
                print(f"没有找到以 {choice} 开头的NCZ_9目录")
        else:
            print("请输入有效的三位数字或目录编号")

if __name__ == '__main__':
    import argparse
    import shutil  # 添加shutil模块用于文件复制
    
    # 交互式选择目录
    selected_directory = select_directory()
    if not selected_directory:
        print("已取消操作")
        exit(0)
    
    print(f"已选择目录: {selected_directory}")
    
    # 解析其他命令行参数
    parser = argparse.ArgumentParser(description='YOLOv8图片查看标记器')
    parser.add_argument('--model', 
                       default='/Users/<USER>/Desktop/bird_feather_project/runs/detect/bird_detection2/weights/best.pt',
                       help='YOLOv8模型路径')
    
    args = parser.parse_args()
    
    # 检查模型文件
    model_path = args.model
    if model_path and not os.path.exists(model_path):
        print(f"警告: 模型文件不存在 - {model_path}")
        print("将在不使用YOLOv8检测的情况下运行")
        model_path = None
    
    # 开始播放
    view_images(selected_directory, model_path)
