import os
import re

def find_ncz_directories(base_path):
    """查找NCZ_9格式的目录"""
    pattern = re.compile(r'^\d{3}NCZ_9$')
    dirs = [d for d in os.listdir(base_path) 
           if os.path.isdir(os.path.join(base_path, d)) and pattern.match(d)]
    return sorted(dirs)

def select_directory(dirs):
    """显示目录列表并获取用户选择"""
    print(f"在 {os.getcwd()} 下找到以下NCZ_9格式目录:")
    for i, d in enumerate(dirs, 1):
        print(f"{i}. {d}")
    
    while True:
        choice = input("输入目录名前3位数字或编号(输入q退出): ").strip()
        if choice.lower() == 'q':
            return None
        
        # 尝试匹配编号
        if choice.isdigit() and 1 <= int(choice) <= len(dirs):
            return dirs[int(choice)-1]
        
        # 尝试匹配前缀
        matches = [d for d in dirs if d.startswith(choice)]
        if len(matches) == 1:
            return matches[0]
        
        print("无效输入，请重试")

if __name__ == "__main__":
    base_path = "/Users/<USER>/Desktop"
    os.chdir(base_path)
    
    ncz_dirs = find_ncz_directories(base_path)
    if not ncz_dirs:
        print("未找到NCZ_9格式的目录")
        exit(1)
        
    selected = select_directory(ncz_dirs)
    if selected:
        print(f"已选择目录: {selected}")
        # 这里可以添加后续处理逻辑
        # 例如调用其他脚本并传递selected参数
    else:
        print("操作已取消")