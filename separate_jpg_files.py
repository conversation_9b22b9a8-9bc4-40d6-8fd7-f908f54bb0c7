#!/usr/bin/env python3
"""
JPG文件分离工具

功能概述：
- 自动分离单JPG文件和NEF+JPG文件对
- 保留NEF+JPG文件对在原目录
- 移动单JPG文件到指定目录

主要功能：
1. 文件分类：
   - 识别单JPG文件(无对应NEF文件)
   - 保留NEF+JPG文件对

2. 文件处理：
   - 创建目标子目录(源目录名_JPG)
   - 移动单JPG文件到目标目录
   - 记录操作日志

3. 目录管理：
   - 自动扫描NCZ_9格式目录
   - 支持交互式目录选择
   - 默认输出到桌面"筛选结果"目录

文件处理逻辑：
- 仅移动无对应NEF的JPG文件
- 保留文件名不变
- 自动创建所需目录结构

使用方法：
1. 直接运行脚本并选择目录
2. 或通过命令行指定源目录：
   python separate_jpg_files.py <源目录>

输出目录结构：
~/Desktop/筛选结果/
└── 源目录名_JPG/
    ├── 单JPG文件1.jpg
    ├── 单JPG文件2.jpg
    └── ...
"""

import os
import shutil
import time
import logging
import glob
import sys
from pathlib import Path

# 设置日志
script_name = os.path.splitext(os.path.basename(__file__))[0]
log_file = os.path.abspath(f"{script_name}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(script_name)

def separate_jpg_files(source_dir, target_base_dir):
    """分离单JPG文件"""
    # 创建目标目录
    source_dir_name = os.path.basename(source_dir)
    target_dir = os.path.join(target_base_dir, f"{source_dir_name}_JPG")
    os.makedirs(target_dir, exist_ok=True)

    # 收集所有文件
    files = [f for f in os.listdir(source_dir) if os.path.isfile(os.path.join(source_dir, f))]
    
    # 分离文件
    jpg_files = set()
    nef_files = set()
    
    for file in files:
        name, ext = os.path.splitext(file)
        ext = ext.lower()
        if ext == '.jpg':
            jpg_files.add(name)
        elif ext == '.nef':
            nef_files.add(name)

    # 计算要移动的单JPG文件
    single_jpgs = jpg_files - nef_files
    
    # 移动文件
    moved_count = 0
    for name in single_jpgs:
        src = os.path.join(source_dir, f"{name}.jpg")
        dst = os.path.join(target_dir, f"{name}.jpg")
        shutil.move(src, dst)
        moved_count += 1
    
    logger.info(f"已移动{moved_count}个JPG文件到: {target_dir}")
    print(f"已移动{moved_count}个JPG文件到: {target_dir}")
    return moved_count

def find_ncz_dirs(base_dir):
    """查找NCZ_9格式目录"""
    return sorted(glob.glob(os.path.join(base_dir, "*NCZ_9*")))

if __name__ == '__main__':
    start_time = time.time()
    base_dir = "/Users/<USER>/Desktop"
    dirs = find_ncz_dirs(base_dir)
    
    if not dirs:
        print(f"在 {base_dir} 下未找到NCZ_9格式目录")
        print("请确保存在NCZ_9开头的目录")
        sys.exit(1)

    print("\n在 /Users/<USER>/Desktop 下找到以下NCZ_9格式目录:")
    for i, dir_path in enumerate(dirs, 1):
        print(f"{i}. {os.path.basename(dir_path)}")
        
    while True:
        choice = input("\n输入目录名前3位数字或编号(输入q退出): ").strip()
        if choice.lower() == 'q':
            sys.exit(0)
            
        try:
            if choice.isdigit() and 1 <= int(choice) <= len(dirs):
                selected_dir = dirs[int(choice)-1]
                break
            elif len(choice) >= 3:
                matched_dirs = [d for d in dirs if os.path.basename(d).startswith(choice)]
                if len(matched_dirs) == 1:
                    selected_dir = matched_dirs[0]
                    break
                elif len(matched_dirs) > 1:
                    print("匹配到多个目录，请更精确输入")
                else:
                    print("未找到匹配目录")
            else:
                print("请输入至少3位数字或有效编号")
        except ValueError:
            print("输入无效，请重试")

    target_dir = "/Users/<USER>/Desktop/筛选结果"
    moved_count = separate_jpg_files(selected_dir, target_dir)
    
    # 计算总耗时
    elapsed_time = time.time() - start_time
    hours = int(elapsed_time // 3600)
    minutes = int((elapsed_time % 3600) // 60)
    seconds = int(elapsed_time % 60)
    print(f"总耗时: {hours:02d}:{minutes:02d}:{seconds:02d}")
