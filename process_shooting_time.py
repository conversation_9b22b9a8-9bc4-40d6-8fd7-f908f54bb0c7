#!/usr/bin/env python3
"""
照片拍摄时间统计工具

功能概述：
- 按拍摄日期统计照片数量
- 提供交互式照片管理功能
- 支持按日期批量删除照片

主要功能：
1. 统计分析：
   - 按日统计照片数量
   - 显示日期排序列表
   - 记录操作日志

2. 交互管理：
   - 按日期编号选择操作
   - 支持确认删除(y/n)
   - 支持退出(q/esc)

3. 目录处理：
   - 自动扫描NCZ_9格式目录
   - 支持命令行指定目录
   - 支持交互式目录选择

使用方法：
1. 命令行模式：
   python3 process_shooting_time.py <照片目录>

2. 交互模式：
   - 直接运行脚本
   - 选择桌面上的NCZ_9目录
   - 按提示操作

交互操作说明：
- 输入日期编号选择要操作的日期
- 输入'y'确认删除，'n'取消
- 输入'q'或'esc'退出程序

日志记录：
- 操作记录保存在process_shooting_time.log
"""

import os
import sys
import argparse
import glob
import logging
from datetime import datetime
from collections import defaultdict

# 配置日志
script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, "process_shooting_time.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def find_ncz_dirs():
    """查找桌面上的NCZ_9格式目录"""
    desktop = os.path.expanduser("~/Desktop")
    dirs = sorted(glob.glob(os.path.join(desktop, "*NCZ_9")))
    return dirs

def get_folder_time(filepath):
    """获取文件夹修改时间"""
    return datetime.fromtimestamp(os.path.getmtime(filepath))

def analyze_shooting_times(directory):
    """分析照片拍摄日期"""
    date_stats = defaultdict(list)
    
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if not os.path.isfile(filepath):
            continue
            
        folder_time = get_folder_time(filepath)
        date_str = folder_time.strftime('%Y-%m-%d')
        date_stats[date_str].append(filepath)
    
    print("\n照片拍摄日期统计:")
    print("=================")
    
    # 显示日期统计
    dates = sorted(date_stats.keys())
    for i, date_str in enumerate(dates):
        count = len(date_stats[date_str])
        print(f"{i+1}) {date_str}: {count}张照片")
        logging.info(f"Date: {date_str}, Photo count: {count}")
    
    print("=================")
    
    # 交互式操作
    while True:
        choice = input("\n输入要删除的日期编号(或q退出): ").strip().lower()
        
        if choice in ('q', 'esc'):
            break
            
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(dates):
                date_str = dates[idx]
                confirm = input(f"确定要删除{date_str}的所有照片({len(date_stats[date_str])}张)? (y/n): ").strip().lower()
                if confirm == 'y':
                    for filepath in date_stats[date_str]:
                        try:
                            os.remove(filepath)
                            logging.info(f"Deleted: {filepath}")
                        except Exception as e:
                            logging.error(f"Failed to delete {filepath}: {str(e)}")
                    logging.info(f"Deleted all photos for date: {date_str} (count: {len(date_stats[date_str])})")
                    print(f"已删除{date_str}的所有照片")
                    del date_stats[date_str]
                    dates.pop(idx)
                    print("\n更新后的统计:")
                    for i, date_str in enumerate(dates):
                        print(f"{i+1}) {date_str}: {len(date_stats[date_str])}张照片")
                else:
                    print("取消删除操作")
            else:
                print("无效的编号")
        except ValueError:
            print("无效输入，请输入数字编号或q退出")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='照片拍摄时间统计工具')
    parser.add_argument('directory', nargs='?', help='要统计的照片目录')
    args = parser.parse_args()
    
    if args.directory:
        if not os.path.isdir(args.directory):
            print(f"错误: {args.directory} 不是有效目录")
            sys.exit(1)
        analyze_shooting_times(args.directory)
    else:
        dirs = find_ncz_dirs()
        if not dirs:
            print("\n在 /Users/<USER>/Desktop 下未找到NCZ_9格式目录")
            print("当前桌面目录内容:")
            for f in os.listdir(desktop):
                print(f" - {f}")
            sys.exit(1)
            
        print("\n在 /Users/<USER>/Desktop 下找到以下NCZ_9格式目录:")
        for i, dir_path in enumerate(dirs, 1):
            print(f"{i}. {os.path.basename(dir_path)}")
            
        while True:
            choice = input("\n输入目录名前3位数字或编号(输入q退出): ").strip()
            if choice.lower() == 'q':
                sys.exit(0)
                
            try:
                if choice.isdigit():
                    idx = int(choice) - 1
                    if 0 <= idx < len(dirs):
                        analyze_shooting_times(dirs[idx])
                        break
                    else:
                        print("无效的编号")
                else:
                    matched = [d for d in dirs if os.path.basename(d).startswith(choice)]
                    if len(matched) == 1:
                        analyze_shooting_times(matched[0])
                        break
                    elif len(matched) > 1:
                        print("匹配到多个目录，请更精确输入")
                    else:
                        print("未找到匹配目录")
            except ValueError:
                print("无效输入，请输入数字编号或目录前缀")